{"semi": true, "trailingComma": "es5", "singleQuote": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.ts", "tailwindFunctions": ["clsx", "cn", "cva"]}